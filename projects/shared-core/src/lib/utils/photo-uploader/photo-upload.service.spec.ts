import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { PhotoUploadService, PhotoStatus } from './photo-upload.service';
import { PhotoUploadApiService } from './photo-upload-api.service';
import { BlobUrlService } from '../services/blob-url.service';
import { DomSanitizer } from '@angular/platform-browser';
import { ChangeDetectorRef } from '@angular/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpEventType, HttpResponse } from '@angular/common/http';
import { of, Subject, throwError } from 'rxjs';
import { PhotoMetadata } from '../../generated/models/photo-metadata';
import { PhotoScale } from '../../generated/models/photo-scale';
import { Status } from '../../generated/models/status';
import { MocksModule } from '@shared-core/testing';

describe('PhotoUploadService', () => {
  let service: PhotoUploadService;
  let photoUploadApiMock: any;
  let blobUrlServiceMock: any;
  let domSanitizerMock: any;
  let changeDetectorRefMock: any;

  beforeEach(() => {
    photoUploadApiMock = {
      targetUrl: 'http://test-api/photos',
      uploadPhoto: jest.fn(),
      getMetadata: jest.fn(),
      removePhoto: jest.fn().mockReturnValue(of({})),
      getPhotoGroup: jest.fn().mockReturnValue(of([])),
      createPhotoGroup: jest.fn().mockReturnValue(of({ id: 'new-group-id' }))
    };

    blobUrlServiceMock = {
      getBlobUrl: jest.fn().mockReturnValue(of({ url: 'blob:test-url' }))
    };

    domSanitizerMock = {
      bypassSecurityTrustUrl: jest.fn().mockReturnValue('safe-url')
    };

    changeDetectorRefMock = {
      detectChanges: jest.fn(),
      markForCheck: jest.fn()
    };

    TestBed.configureTestingModule({
      imports: [MocksModule, HttpClientTestingModule],
      providers: [
        PhotoUploadService,
        { provide: PhotoUploadApiService, useValue: photoUploadApiMock },
        { provide: BlobUrlService, useValue: blobUrlServiceMock },
        { provide: DomSanitizer, useValue: domSanitizerMock },
        { provide: ChangeDetectorRef, useValue: changeDetectorRefMock }
      ]
    });

    service = TestBed.inject(PhotoUploadService);

    // Mock window.navigator for platform detection
    Object.defineProperty(navigator, 'userAgent', {
      value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      configurable: true
    });

    // Mock window.setInterval and clearInterval
    jest.spyOn(window, 'setInterval').mockReturnValue(123 as unknown as NodeJS.Timeout);
    jest.spyOn(window, 'clearInterval').mockImplementation();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('initialiseExistingPhotos', () => {
    it('should initialize existing photos from API', fakeAsync(() => {
      // Arrange
      const mockPhotoGroups = [
        {
          id: '123',
          photoSets: [
            {
              originalId: 'photo1',
              extraLarge: {
                id: 'extraLarge1',
                height: 800,
                width: 600
              },
              thumbnail: {
                id: 'thumb1',
                height: 100,
                width: 75
              }
            }
          ]
        }
      ];

      service.photoGroup = { id: '123', photosToRemove: [] };
      photoUploadApiMock.getPhotoGroup.mockReturnValue(of(mockPhotoGroups));

      // Act
      let result;
      service.initialiseExistingPhotos(true).subscribe(data => {
        result = data;
      });
      tick();

      // Assert
      expect(photoUploadApiMock.getPhotoGroup).toHaveBeenCalledWith(true, service.photoGroup);
      expect(service.photos).toHaveLength(1);
      expect(service.photos[0].id).toBe('photo1');
      expect(service.photos[0].status).toBe(PhotoStatus.Success);
    }));

    it('should filter out photos that are marked for removal', fakeAsync(() => {
      // Arrange
      const mockPhotoGroups = [
        {
          id: '123',
          photoSets: [
            {
              originalId: 'photo1',
              extraLarge: { id: 'extraLarge1', height: 800, width: 600 },
              thumbnail: { id: 'thumb1', height: 100, width: 75 }
            },
            {
              originalId: 'photo2',
              extraLarge: { id: 'extraLarge2', height: 800, width: 600 },
              thumbnail: { id: 'thumb2', height: 100, width: 75 }
            }
          ]
        }
      ];

      service.photoGroup = { id: '123', photosToRemove: ['photo1'] };
      photoUploadApiMock.getPhotoGroup.mockReturnValue(of(mockPhotoGroups));

      // Act
      service.initialiseExistingPhotos(true).subscribe();
      tick();

      // Assert
      expect(service.photos).toHaveLength(1);
      expect(service.photos[0].id).toBe('photo2');
    }));

    it('should handle API errors gracefully', fakeAsync(() => {
      // Arrange
      service.photoGroup = { id: '123' };
      photoUploadApiMock.getPhotoGroup.mockReturnValue(throwError('NoonaApiError: Something went wrong'));

      // Add a photo that would be affected by the error
      service.photos = [{ id: 'photo1', status: PhotoStatus.Waiting }];
      jest.spyOn(service.uploadedPhotoSub, 'next');

      // Act
      let error;
      service.initialiseExistingPhotos(true).subscribe(
        () => {},
        err => {
          error = err;
        }
      );
      tick();

      // Assert
      expect(error).toBe('NoonaApiError: Something went wrong');
      expect(service.photos[0].status).toBe(PhotoStatus.Error);
      expect(service.photos[0].errorMessageKey).toBe('general.photoUpload.serverError');
      expect(service.uploadedPhotoSub.next).toHaveBeenCalled();
    }));
  });

  describe('checkPhotoGroup', () => {
    it('should return existing photoGroup if available', fakeAsync(() => {
      // Arrange
      service.photoGroup = { id: 'existing-group' };

      // Act
      let result;
      service.checkPhotoGroup().subscribe(group => {
        result = group;
      });
      tick();

      // Assert
      expect(result).toEqual({ id: 'existing-group' });
      expect(photoUploadApiMock.createPhotoGroup).not.toHaveBeenCalled();
    }));

    it('should create a new photoGroup if none exists', fakeAsync(() => {
      // Arrange
      service.photoGroup = undefined;
      service.patientId = 'patient-123';

      // Act
      let result;
      service.checkPhotoGroup().subscribe(group => {
        result = group;
      });
      tick();

      // Assert
      expect(photoUploadApiMock.createPhotoGroup).toHaveBeenCalledWith('patient-123');
      expect(result).toEqual({ id: 'new-group-id' });
      expect(service.photoGroup).toEqual({ id: 'new-group-id' });
    }));
  });

  describe('setFiles', () => {
    it('should handle valid files and start upload process', fakeAsync(() => {
      // Arrange
      const files = [new File(['content'], 'test.jpg', { type: 'image/jpeg' })];

      const uploadProgressEvent = {
        type: HttpEventType.UploadProgress,
        loaded: 50,
        total: 100
      };

      const uploadCompleteEvent = {
        type: HttpEventType.Response,
        body: { photoId: 'uploaded-photo-id' }
      };

      const metadata: PhotoMetadata = {
        id: 'metadata-id',
        height: 300,
        width: 400,
        status: Status.READY
      };

      photoUploadApiMock.uploadPhoto.mockReturnValue(of(uploadProgressEvent, uploadCompleteEvent));
      photoUploadApiMock.getMetadata.mockReturnValue(of(metadata));

      jest.spyOn(service, 'startRetryInterval' as any);
      jest.spyOn(service.emitCheckUploadAllow$, 'next');
      jest.spyOn(service.uploadedPhotoSub, 'next');

      // Act
      service.setFiles(files);
      tick();

      // Assert
      expect(service['startRetryInterval']).toHaveBeenCalled();
      expect(service.photos).toHaveLength(1);
      expect(service.photos[0].file).toBe(files[0]);
      expect(service.photos[0].progress).toBe(50);
      expect(service.photos[0].id).toBe('uploaded-photo-id');
      expect(service.photos[0].status).toBe(PhotoStatus.Success);
      expect(service.uploadedPhotoSub.next).toHaveBeenCalled();
      expect(service.emitCheckUploadAllow$.next).toHaveBeenCalled();
      expect(photoUploadApiMock.uploadPhoto).toHaveBeenCalledWith(
        expect.objectContaining({
          file: files[0]
        }),
        service.photoGroup
      );
    }));

    it('should handle file size exceeding limit', fakeAsync(() => {
      // Arrange
      const MAX_FILE_SIZE = 6 * 1024 * 1024; // 6 MB
      const largeFile = new File(['a'.repeat(MAX_FILE_SIZE + 1)], 'large.jpg', { type: 'image/jpeg' });

      jest.spyOn(service.uploadedPhotoSub, 'next');

      // Act
      service.setFiles([largeFile]);
      tick();

      // Assert
      expect(service.photos).toHaveLength(1);
      expect(service.photos[0].status).toBe(PhotoStatus.Error);
      expect(service.photos[0].errorMessageKey).toBe('general.photoUpload.fileTooBig');
      expect(service.uploadedPhotoSub.next).toHaveBeenCalledWith(
        expect.objectContaining({
          errorMessageKey: 'general.photoUpload.fileTooBig'
        })
      );
    }));

    it('should handle server errors during upload', fakeAsync(() => {
      // Arrange
      const file = new File(['content'], 'test.jpg', { type: 'image/jpeg' });

      const uploadProgressEvent = {
        type: HttpEventType.UploadProgress,
        loaded: 50,
        total: 100
      };

      const uploadCompleteEvent = {
        type: HttpEventType.Response,
        body: { photoId: 'failed-photo-id' }
      };

      const failedMetadata: PhotoMetadata = {
        id: 'metadata-id',
        height: 300,
        width: 400,
        status: Status.FAILED
      };

      photoUploadApiMock.uploadPhoto.mockReturnValue(of(uploadProgressEvent, uploadCompleteEvent));
      photoUploadApiMock.getMetadata.mockReturnValue(of(failedMetadata));

      jest.spyOn(service.uploadedPhotoSub, 'next');

      // Act
      service.setFiles([file]);
      tick();

      // Assert
      expect(service.photos[0].status).toBe(PhotoStatus.Error);
      expect(service.photos[0].errorMessageKey).toBe('general.photoUpload.serverError');
      expect(photoUploadApiMock.removePhoto).toHaveBeenCalled();
    }));
  });

  describe('removePhoto', () => {
    it('should remove photo from photos array and call API', fakeAsync(() => {
      // Arrange
      const photo = {
        id: 'photo-to-remove',
        status: PhotoStatus.Success,
        sub: { unsubscribe: jest.fn() }
      };
      service.photos = [photo, { id: 'another-photo' }];

      // Act
      let result;
      service.removePhoto(photo, 0).subscribe(res => {
        result = res;
      });
      tick();

      // Assert
      expect(service.photos).toHaveLength(1);
      expect(service.photos[0].id).toBe('another-photo');
      expect(photo.sub.unsubscribe).toHaveBeenCalled();
      expect(photoUploadApiMock.removePhoto).toHaveBeenCalledWith(photo);
      expect(result).toBeTruthy();
    }));

    it('should not call API for photos with waiting status and no ID', fakeAsync(() => {
      // Arrange
      const photo = {
        status: PhotoStatus.Waiting, // No ID, just waiting status
        sub: { unsubscribe: jest.fn() }
      };
      service.photos = [photo];

      // Act
      service.removePhoto(photo, 0).subscribe();
      tick();

      // Assert
      expect(service.photos).toHaveLength(0);
      expect(photo.sub.unsubscribe).toHaveBeenCalled();
      expect(photoUploadApiMock.removePhoto).not.toHaveBeenCalled();
    }));
  });

  describe('retryPendingUploads', () => {
    it('should retry pending uploads up to maximum attempts', fakeAsync(() => {
      // Arrange
      const pendingPhoto = {
        id: 'pending-photo',
        file: new File(['content'], 'test.jpg', { type: 'image/jpeg' }),
        status: PhotoStatus.Waiting,
        retryAttempts: 0,
        sub: { unsubscribe: jest.fn() }
      };

      service.photos = [pendingPhoto];

      // Mock the removePhoto method
      jest.spyOn(service, 'removePhoto').mockReturnValue(of(true));

      // Mock the createAndUploadPhotoItem private method
      const newPhotoItem = {
        id: 'pending-photo-retry',
        file: pendingPhoto.file,
        status: PhotoStatus.Waiting,
        retryAttempts: 1
      };
      jest.spyOn(service as any, 'createAndUploadPhotoItem').mockReturnValue(newPhotoItem);

      // Act
      service.retryPendingUploads();
      tick();

      // Assert
      expect(service.removePhoto).toHaveBeenCalledWith(pendingPhoto, 0);
      expect(service['createAndUploadPhotoItem']).toHaveBeenCalledWith(pendingPhoto.file, 1);
      expect(service.photos).toContain(newPhotoItem);
    }));

    it('should not retry photos that have reached max retry attempts', fakeAsync(() => {
      // Arrange
      const MAX_RETRY_ATTEMPTS = 3; // Matching the constant in the service
      const exhaustedPhoto = {
        id: 'exhausted-photo',
        file: new File(['content'], 'test.jpg', { type: 'image/jpeg' }),
        status: PhotoStatus.Waiting,
        retryAttempts: MAX_RETRY_ATTEMPTS,
        sub: { unsubscribe: jest.fn() }
      };

      service.photos = [exhaustedPhoto];

      jest.spyOn(service, 'removePhoto');

      // Act
      service.retryPendingUploads();
      tick();

      // Assert
      expect(service.removePhoto).not.toHaveBeenCalled();
      expect(service.photos).toContain(exhaustedPhoto);
    }));

    it('should stop retry interval when all uploads are complete or exhausted', fakeAsync(() => {
      // Arrange
      service.photos = [
        { id: 'photo1', status: PhotoStatus.Success },
        { id: 'photo2', status: PhotoStatus.Error, retryAttempts: 3 }
      ];

      jest.spyOn(service, 'stopRetryInterval');

      // Act
      service.retryPendingUploads();
      tick();

      // Assert
      expect(service.stopRetryInterval).toHaveBeenCalled();
    }));
  });

  describe('uploadedPhotoSub', () => {
    it('should emit photo updates through uploadedPhoto$ observable', () => {
      // Arrange
      const photo = {
        id: 'test-photo',
        status: PhotoStatus.Success,
        thumbnail: { src: 'safe-url', w: 100, h: 100 }
      };

      // Act & Assert
      service.uploadedPhoto$.subscribe(emittedPhoto => {
        expect(emittedPhoto).toEqual(photo);
      });

      service.uploadedPhotoSub.next(photo);
    });

    it('should handle null values in uploadedPhoto$ observable', () => {
      // Arrange
      let emittedValue = 'not set';

      // Act & Assert
      service.uploadedPhoto$.subscribe(value => {
        emittedValue = value;
      });

      service.uploadedPhotoSub.next(null);
      expect(emittedValue).toBeNull();
    });
  });

  describe('platform detection', () => {
    it('should detect Android devices correctly', () => {
      // Arrange
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
        configurable: true
      });

      // Act - Create a new instance to trigger platform detection
      const androidService = TestBed.inject(PhotoUploadService);

      // Assert - Using private property access for testing
      expect((androidService as any).isAndroid).toBe(true);
    });

    it('should not identify iOS as Android', () => {
      // Arrange
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15',
        configurable: true
      });

      // Act - Create a new instance to trigger platform detection
      const iosService = TestBed.inject(PhotoUploadService);

      // Assert - Using private property access for testing
      expect((iosService as any).isAndroid).toBe(false);
    });
  });

  describe('startRetryInterval and stopRetryInterval', () => {
    it('should start retry interval only on Android devices', () => {
      // Arrange
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
        configurable: true
      });

      const androidService = TestBed.inject(PhotoUploadService);
      jest.spyOn(window, 'setInterval').mockClear();

      // Act
      (androidService as any).startRetryInterval();

      // Assert
      expect(window.setInterval).toHaveBeenCalled();
    });

    it('should not start retry interval on non-Android devices', () => {
      // Arrange
      Object.defineProperty(navigator, 'userAgent', {
        value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_3 like Mac OS X) AppleWebKit/605.1.15',
        configurable: true
      });

      const iosService = TestBed.inject(PhotoUploadService);
      jest.spyOn(window, 'setInterval').mockClear();

      // Act
      (iosService as any).startRetryInterval();

      // Assert
      expect(window.setInterval).not.toHaveBeenCalled();
    });

    it('should clear interval when stopRetryInterval is called', () => {
      // Act
      service.stopRetryInterval();

      // Assert
      expect(window.clearInterval).toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('should set photo error with correct message for server errors', () => {
      // Arrange
      const photo = { id: 'error-photo', status: PhotoStatus.Waiting };
      jest.spyOn(service.uploadedPhotoSub, 'next');

      // Act
      (service as any).handleServerError(photo);

      // Assert
      expect(photo.status).toBe(PhotoStatus.Error);
      expect(photo.errorMessageKey).toBe('general.photoUpload.serverError');
      expect(service.uploadedPhotoSub.next).toHaveBeenCalledWith(photo);
    });

    it('should set photo error with correct message for file size errors', () => {
      // Arrange
      const photo = { id: 'large-file-photo', status: PhotoStatus.Waiting };
      jest.spyOn(service.uploadedPhotoSub, 'next');

      // Act
      (service as any).handleErrorFileTooBig(photo);

      // Assert
      expect(photo.status).toBe(PhotoStatus.Error);
      expect(photo.errorMessageKey).toBe('general.photoUpload.fileTooBig');
      expect(service.uploadedPhotoSub.next).toHaveBeenCalledWith(photo);
    });
  });

  describe('updateUploadStatus', () => {
    it('should emit upload in progress when photos are being uploaded', () => {
      // Arrange
      service.photos = [
        { id: 'photo1', status: PhotoStatus.Waiting },
        { id: 'photo2', status: PhotoStatus.Success }
      ];

      jest.spyOn(service.uploadInProgress, 'emit');

      // Act
      (service as any).updateUploadStatus();

      // Assert
      expect(service.uploadInProgress.emit).toHaveBeenCalledWith(true);
    });

    it('should emit upload not in progress when all photos are complete', () => {
      // Arrange
      service.photos = [
        { id: 'photo1', status: PhotoStatus.Success },
        { id: 'photo2', status: PhotoStatus.Error }
      ];

      jest.spyOn(service.uploadInProgress, 'emit');

      // Act
      (service as any).updateUploadStatus();

      // Assert
      expect(service.uploadInProgress.emit).toHaveBeenCalledWith(false);
    });
  });

  describe('ngOnDestroy', () => {
    it('should clean up resources on destroy', () => {
      // Arrange
      const sub1 = { unsubscribe: jest.fn() };
      const sub2 = { unsubscribe: jest.fn() };

      service.photos = [
        { id: 'photo1', sub: sub1 },
        { id: 'photo2', sub: sub2 }
      ];

      jest.spyOn(service.destroy$, 'next');
      jest.spyOn(service.destroy$, 'complete');
      jest.spyOn(service.emitCheckUploadAllow$, 'complete');

      // Act
      service.ngOnDestroy();

      // Assert
      expect(service.stopRetryInterval).toHaveBeenCalled();
      expect(service.destroy$.next).toHaveBeenCalled();
      expect(service.destroy$.complete).toHaveBeenCalled();
      expect(service.emitCheckUploadAllow$.complete).toHaveBeenCalled();
      expect(sub1.unsubscribe).toHaveBeenCalled();
      expect(sub2.unsubscribe).toHaveBeenCalled();
    });
  });
});

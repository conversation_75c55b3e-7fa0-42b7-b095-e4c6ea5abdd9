import {
  Component,
  DestroyRef,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  Output,
  SimpleChanges,
  ViewEncapsulation,
  inject
} from '@angular/core';
import { PainPointerSide } from '../../form-engine/models/pain-pointer-side.enum';
import { Gender } from '../../generated/models/gender';
import { ConvertHyphenedToCapitalPipe } from '../../pipes/convert-hyphened-to-capital.pipe';
import { HyphenedPipe } from '../../pipes/hyphened.pipe';
import { I18NPipe } from '../../pipes/i18n.pipe';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { PainPointerService } from './pain-pointer.service';

const bodyPartOrder = {
  [PainPointerSide.FRONT]: [
    'head',
    'forehead',
    'right-under-chin',
    'left-under-chin',
    'neck',
    'right-solis-pit',
    'left-solis-pit',
    'chest',
    'right-breast',
    'left-breast',
    'right-ribs',
    'left-ribs',
    'abdomen',
    'right-groin',
    'left-groin',
    'right-hip',
    'left-hip',
    'genitalia',
    'right-front-thigh',
    'right-knee',
    'right-shin',
    'right-ankle',
    'left-front-thigh',
    'left-knee',
    'left-shin',
    'left-ankle',
    'right-shoulder',
    'right-armpit-front',
    'right-front-upper-arm',
    'right-front-elbow',
    'right-front-arm',
    'right-palm',
    'left-shoulder',
    'left-armpit-front',
    'left-front-upper-arm',
    'left-front-elbow',
    'left-front-arm',
    'left-palm'
  ],
  [PainPointerSide.BACK]: [
    'back-of-the-head',
    'nape',
    'back-upper',
    'back-middle',
    'back-lower',
    'left-back-thigh',
    'left-back-of-knee',
    'left-calf',
    'left-achilles',
    'right-back-thigh',
    'right-back-of-knee',
    'right-calf',
    'right-achilles',
    'left-point-of-shoulder',
    'left-armpit-back',
    'left-upper-arm-back',
    'left-elbow',
    'left-arm-back',
    'left-wrist',
    'right-point-of-shoulder',
    'right-armpit-back',
    'right-upper-arm-back',
    'right-elbow',
    'right-arm-back',
    'right-wrist'
  ]
};

@Component({
  selector: 'pain-pointer',
  templateUrl: './pain-pointer.component.html',
  styleUrls: ['./pain-pointer.scss'],
  encapsulation: ViewEncapsulation.None
})
export class PainPointerComponent implements OnChanges, OnDestroy {
  @Input() painPointerWidth = 100;
  @Input() painPointerHeight = 250;
  @Input() gender: Gender;
  @Input() painLocations: string[];
  @Input() side: PainPointerSide;
  @Input() enabledLocations: string[];
  @Input() view = true;
  @Input() selectedParts: string[] = [];

  @Output() partToggled = new EventEmitter<string>();

  destroyRef = inject(DestroyRef);

  private diagram: JQuery<HTMLElement>;
  private initialized = false;
  private locationIds: string[];

  constructor(
    private readonly hyphenedPipe: HyphenedPipe,
    private readonly elementRef: ElementRef,
    private readonly hyphenedCapitalPipe: ConvertHyphenedToCapitalPipe,
    private readonly i18nPipe: I18NPipe,
    private readonly painPointerService: PainPointerService
  ) {}

  public ngOnChanges(change: SimpleChanges) {
    if (this.gender && this.painLocations && this.side && this.painPointerWidth && this.painPointerHeight && !this.initialized) {
      this.initialized = true;
      this.locationIds = this.painLocations.map(location => {
        return this.hyphenedPipe.transform(location);
      });

      if (!this.diagram || change.gender || change.painPointerWidth || change.painPointerHeight || change.side) {
        this.loadAndAddDiagram();
      }
    }
  }

  ngOnDestroy() {
    if (!this.view) {
      this.toggleListener(false);
    }
  }

  public updatePainPointer(diagram, locations, side) {
    this.removeOtherSide(diagram, side);
    this.hideSpots(diagram);
    this.hidePainAreas(diagram);
    this.showSpots(diagram, locations, side);

    if (!this.view) {
      this.toggleListener(true);
    }
  }

  private toggleListener(enable: boolean) {
    if (!this.diagram) return;

    const bodyPartsContainer = this.diagram.find(`#body-parts-${this.side}`);
    const bodyParts = bodyPartsContainer.find('> g');
    console.log('Body parts:', bodyParts);
    if (!bodyParts?.length) return;

    const orderedBodyParts = bodyPartOrder[this.side]
      .map(partId => bodyParts.filter(`#${partId}`)[0])
      .filter(p => p && this.locationIds.includes(p.id));

    orderedBodyParts.forEach(bodyPart => {
      $(bodyPart).detach();
      bodyPartsContainer.append(bodyPart);
      bodyPart.classList.add('body-part');
      const locationName = this.hyphenedCapitalPipe.transform(bodyPart.id);
      const ariaLabel = this.i18nPipe.transform(`fe.bpipainlocations.${locationName}.label`);
      const elem = $(bodyPart);
      elem.attr('tabindex', 0);
      elem.attr('role', 'checkbox');
      elem.attr('aria-label', ariaLabel);
      const isSelected = this.selectedParts.includes(locationName);
      elem.attr('aria-checked', isSelected ? 'true' : 'false');

      if (enable) {
        elem.on('mouseover', event => {
          return this.mouseOver(event);
        });
        elem.on('mouseleave', event => {
          return this.mouseLeave(event);
        });
        elem.on('click', event => this.toggleSelectedPoint(event));
        elem.on('keypress', event => this.keyDown(event));
      } else {
        elem.attr('disabled', 'true');
        elem.off('mouseover');
        elem.off('mouseleave');
        elem.off('click');
        elem.off('keypress');
      }
    });
  }

  private keyDown(event: JQuery.TriggeredEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.toggleSelectedPoint(event);
    }
  }

  private toggleSelectedPoint(event: JQuery.TriggeredEvent) {
    const elem = $(event.currentTarget);
    const locationId = elem.attr('id');
    const locationName = this.hyphenedCapitalPipe.transform(locationId);
    const isSelected = !this.selectedParts.includes(locationName); // isSelected is true if part was not selected already
    elem.attr('aria-checked', isSelected ? 'true' : 'false');

    if (isSelected) {
      this.setOpacity(locationId, 0.5, 1, 0);
    } else {
      this.setOpacity(locationId, 0, 0, 0.33);
    }
    this.partToggled.emit(locationId);
  }

  public showSpots(svg: JQuery<HTMLElement>, locations: string[], side?: PainPointerSide) {
    if (side) {
      svg.find('#pain-spots-' + side + ' > *').attr('opacity', 0);
      svg.find('#pain-body-parts-' + side + ' > *').attr('opacity', 0);
    }
    locations.forEach(location => {
      const originalName = location;
      location = this.hyphenedPipe.transform(location);
      if (this.view || this.selectedParts.includes(originalName)) {
        this.setOpacity(location, 0.5, 1, 0);
      } else {
        this.setOpacity(location, 0, 0, 0.33);
      }
    });
  }

  public hideSpots(svg?: JQuery<HTMLElement>) {
    const diagram = svg ?? this.diagram;
    diagram.find('#pain-spots-back *').attr('opacity', 0);
    diagram.find('#pain-body-parts-back > g').attr('opacity', 0);
    diagram.find('#pain-spots-front *').attr('opacity', 0);
    diagram.find('#pain-body-parts-front > g').attr('opacity', 0);
  }

  public removeOtherSide(svg?: JQuery<HTMLElement>, side?: PainPointerSide) {
    const diagram = svg ?? this.diagram;
    if (side === 'front') {
      diagram.find('#body-back-bg, #body-back, #pain-spots-back, #pain-body-parts-back, #body-parts-back').remove();
    } else {
      diagram.find('#body-front, #body-front_1_, #pain-spots-front, #pain-body-parts-front, #body-parts-front').remove();
    }
  }

  public hidePainAreas(svg?: JQuery<HTMLElement>) {
    const diagram = svg ?? this.diagram;
    diagram.find('#body-parts-front g, #body-parts-back g').attr('opacity', 0);
  }

  // -------------------------------------------------------------
  // ------------- Init SVG --------------------------------------
  // -------------------------------------------------------------

  private loadAndAddDiagram() {
    const gender = this.gender ?? Gender.FEMALE;

    this.painPointerService
      .getDiagram(gender)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(svg => {
        const diagram = $(svg).filter('svg').first();
        const leftHandSideText = this.i18nPipe.transform(this.side === PainPointerSide.BACK ? 'general.left' : 'general.right');
        const rightHandSideText = this.i18nPipe.transform(this.side === PainPointerSide.BACK ? 'general.right' : 'general.left');
        const leftTextElement = diagram.find('#text-1');
        const rightTextElement = diagram.find('#text-2');
        this.splitGradients(diagram);
        diagram.attr('width', this.painPointerWidth);
        diagram.attr('height', this.painPointerHeight);
        diagram.attr('role', 'group');

        leftTextElement.text(leftHandSideText);
        rightTextElement.text(rightHandSideText);
        leftTextElement.attr('aria-hidden', 'true');
        rightTextElement.attr('aria-hidden', 'true');

        this.removeDisabledSpots(diagram);

        this.diagram = diagram;

        const containerClass = '.diagram-container';
        $(containerClass, this.elementRef.nativeElement).children().remove();
        $(containerClass, this.elementRef.nativeElement).append(this.diagram);
        this.updatePainPointer(this.diagram, this.painLocations, this.side);
      });
  }

  private removeDisabledSpots(diagram) {
    if (diagram?.get(0)) {
      const enabledPoints = this.enabledLocations || [];
      const painSpotsFront = diagram.find('#pain-spots-front path');
      const painSpotsBack = diagram.find('#pain-spots-back path');
      const painBodyPartsFront = diagram.find('#pain-body-parts-front > g');
      const painBodyPartsBack = diagram.find('#pain-body-parts-back > g');
      const bodyPartsFront = diagram.find('#body-parts-front > g');
      const bodyPartsBack = diagram.find('#body-parts-back > g');

      this.checkSpots(painSpotsFront, enabledPoints, '-pain-area');
      this.checkSpots(painSpotsBack, enabledPoints, '-pain-area');
      this.checkSpots(painBodyPartsFront, enabledPoints, '-pain');
      this.checkSpots(painBodyPartsBack, enabledPoints, '-pain');
      this.checkSpots(bodyPartsFront, enabledPoints);
      this.checkSpots(bodyPartsBack, enabledPoints);
    }
  }

  private checkSpots(spots: JQuery<HTMLElement>, enabledLocations: string[], suffix?: string) {
    suffix = suffix ?? '';
    spots.each(idx => {
      const element = spots.get(idx);
      const spotName = this.hyphenedCapitalPipe.transform(element.id.replace(suffix, ''));
      if (!enabledLocations.includes(spotName)) {
        element.parentNode.removeChild(element);
      }
    });
  }

  private splitGradients(diagram: JQuery<HTMLElement>) {
    const gradientElementId = 'pain-pointer-gradients';
    if ($('#' + gradientElementId).length === 0) {
      const svg = $(this.createSvgDocument());
      svg.attr('id', gradientElementId);
      svg.append(diagram.find('radialGradient'));
      svg.attr('aria-hidden', 'true');
      svg.css('position', 'absolute');
      $(document.body).prepend(svg);
    }
  }

  private createSvgDocument() {
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '0');
    svg.setAttribute('height', '0');
    svg.setAttributeNS('http://www.w3.org/2000/xmlns/', 'xmlns:xlink', 'http://www.w3.org/1999/xlink');
    return svg;
  }

  private mouseOver(event?: JQuery.MouseOverEvent) {
    if (!event?.currentTarget) {
      return;
    }

    const id = event.currentTarget.id;
    const hyphened = this.hyphenedCapitalPipe.transform(id);
    if (this.selectedParts.includes(hyphened)) {
      this.setOpacityWithoutLocation(id, 1, 1);
    } else {
      this.setOpacityForPainArea(id, 1);
    }
  }

  private mouseLeave(event?: JQuery.MouseLeaveEvent) {
    if (!event?.currentTarget) {
      return;
    }

    const id = event.currentTarget.id;
    const hyphened = this.hyphenedCapitalPipe.transform(id);
    if (this.selectedParts.includes(hyphened)) {
      this.setOpacity(id, 0.5, 1, 0);
    } else {
      this.setOpacity(id, 0, 0, 0.33);
    }
  }

  private setOpacity(locationId: string, painOpacity: number, painAreaOpacity: number, locationOpacity: number) {
    this.diagram.find('#' + locationId + '-pain').attr('opacity', painOpacity);
    this.diagram.find('#' + locationId + '-pain-area').attr('opacity', painAreaOpacity);
    this.diagram.find('#' + locationId).attr('opacity', locationOpacity);
  }

  private setOpacityWithoutLocation(locationId: string, painOpacity: number, painAreaOpacity: number) {
    this.diagram.find('#' + locationId + '-pain').attr('opacity', painOpacity);
    this.diagram.find('#' + locationId + '-pain-area').attr('opacity', painAreaOpacity);
  }

  private setOpacityForPainArea(locationId: string, painAreaOpacity: number) {
    this.diagram.find('#' + locationId + '-pain-area').attr('opacity', painAreaOpacity);
  }
}
